{"private": true, "name": "emmysoft-api", "version": "4.2.0", "description": "", "main": "dist/index.js", "engines": {"node": ">=23", "pnpm": ">=8"}, "scripts": {"build": "npm run prisma:generate && npm run transpile && npm run copyfiles", "clean": "rimraf ./dist", "copyfiles": "copyfiles -Vauf src/routes/v2/docs/specification.yml dist/routes/v2/docs", "lint:fix": "eslint --fix ./", "lint": "eslint ./", "prisma": "npm run prisma:introspect && npm run prisma:generate", "prisma:generate": "prisma generate", "prisma:format": "prisma format", "prisma:introspect": "prisma db pull", "prisma:migrate:create": "prisma migrate dev --create-only", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:db:push": "prisma db push", "prisma:db:pull": "prisma db pull", "prisma:reset": "prisma migrate reset", "dev": "nodemon -w ./src -r tsconfig-paths/register -w .env --inspect ./src/index.ts", "start": "node ./dist/index.js", "test": "jest --coverage", "test:cov": "jest --ci --coverage 2> test-result.txt", "generate:versionfile": "git rev-parse HEAD > ./dist/version.txt", "transpile": "tsc --project ./tsconfig.json", "depcheck": "npx depcheck ./", "postinstall": "npm run prisma:generate"}, "author": "dfind [https://dfind.com]", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "UNLICENSED", "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/jest": "^29.5.14", "@types/jsforce": "^1.11.5", "@types/jwk-to-pem": "^2.0.3", "@types/md5": "^2.3.5", "@types/mime-types": "^2.1.4", "@types/node": "^22.13.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "@typescript-eslint/parser": "^5.48.2", "copyfiles": "^2.4.1", "eslint": "^8.32.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.7.0", "jest-junit": "^15.0.0", "nodemon": "^3.1.9", "prettier": "^2.8.3", "rimraf": "^6.0.1", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2"}, "dependencies": {"@logtail/node": "^0.5.2", "@logtail/winston": "^0.5.2", "@prisma/client": "^6.4.1", "@sentry/integrations": "^7.114.0", "@sentry/node": "^7.110.1", "@sentry/tracing": "^7.120.3", "@types/express": "^5.0.0", "@types/joi": "^17.2.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "aws-jwt-verify": "^4.0.1", "cls-rtracer": "^2.6.3", "compression": "^1.7.5", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "express": "^4.21.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.5.0", "express-winston": "^4.2.0", "firebase-admin": "^12.7.0", "helmet": "^8.0.0", "http-status": "^1.7.4", "joi": "^17.13.3", "joi-password": "^4.2.0", "jsforce": "^1.11.1", "md5": "^2.3.0", "mime-types": "^2.1.35", "nanoid": "^5.1.2", "prisma": "^6.4.1", "sharp": "0.34.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.0.5", "winston": "^3.17.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "silent": true, "coverageDirectory": "../coverage", "testEnvironment": "node", "reporters": ["default", ["jest-junit", {"outputDirectory": "./test-report", "outputName": "unit-test-report.xml"}]]}, "pnpm": {"onlyBuiltDependencies": ["@prisma/client", "@prisma/engines", "@scarf/scarf", "prisma", "protobufjs"]}}