###############
# BUILD STAGE #
###############

# node:alpine does not work on arm64 (e.g. Apple M1) therefore we use node:lts instead
# which is based on Debian
# The RUN STAGE has to be on the same architecture as prisma downloads the binaries for that specific architecture
FROM node:23 AS development

# Select working directory
WORKDIR /usr/src/app

# The .npmrc file is needed to access private npm packages
COPY package.json .npmrc pnpm-lock.yaml ./
RUN npm install -g pnpm@8 &&\
  pnpm fetch

# Copy files before installing dependencies with pnpm
COPY . .
RUN pnpm install -r --ignore-scripts &&\
  pnpm prisma:generate &&\
  pnpm build &&\
  git rev-parse HEAD > ./dist/version.txt

#############
# RUN STAGE #
#############

FROM node:23 AS production

WORKDIR /usr/src/app

COPY --from=development /usr/src/app/dist ./dist
COPY --from=development /usr/src/app/node_modules/.pnpm/ node_modules/.pnpm/
# Copy the generated Prisma client from development stage
COPY --from=development /usr/src/app/node_modules/.prisma node_modules/.prisma
COPY --from=development /usr/src/app/.prisma .prisma
COPY package.json pnpm-lock.yaml ./
COPY prisma prisma

# Debug command for logging the folder content
# RUN ls -la ./prisma && exit 1

# Install pnpm
RUN npm install -g pnpm@8

# Install production dependencies
RUN pnpm fetch --prod &&\
  pnpm install -r --prod --ignore-scripts &&\
  pnpm prisma generate

# Start the server
EXPOSE 80

# Set the entrypoint
ENTRYPOINT ["sh", "-c", "pnpx prisma migrate deploy && pnpm start"]
