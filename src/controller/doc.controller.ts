/* eslint-disable @typescript-eslint/no-explicit-any */

import { Document as PrismaDocument } from '@prisma/client'
import prisma from '../config/prisma'
import Document from '../model/mappings/Doc'
import { log } from '../config/logger'
import sharp from 'sharp'

export function getDocument(id: string, queryData = true): Promise<PrismaDocument | null> {
  return prisma.document.findFirst({
    where: {
      Id: id
    },
    select: {
      Id: true,
      Title: true,
      DocumentType: true,
      Timestamp: true,
      MimeType: true,
      Data: queryData,
      ByteSize: true,
      xfdf: true,
      shared: true,
      versionNumber: true,
      Reference: true
    }
  })
}

export function getDocumentsByIds(ids: string[], queryData = true, sharedOnly = true): Promise<PrismaDocument[]> {
  const queryOptions: any = {
    where: {
      Id: {
        in: ids
      }
    },
    select: {
      Id: true,
      Title: true,
      DocumentType: true,
      Timestamp: true,
      MimeType: true,
      Data: queryData,
      ByteSize: true,
      xfdf: true,
      shared: true,
      Reference: true
    }
  }
  if (sharedOnly) {
    queryOptions.where.shared = true
  }
  return prisma.document.findMany(queryOptions)
}

export function getDocumentsByIdsWithData(
  ids: string[],
  maxByteSize: number,
  types: string[],
  sharedOnly = true
): Promise<PrismaDocument[]> {
  const queryOptions: any = {
    where: {
      Id: {
        in: ids
      },
      ByteSize: {
        lte: maxByteSize
      },
      DocumentType: {
        in: types
      }
    }
  }
  if (sharedOnly) {
    queryOptions.where.shared = true
  }
  return prisma.document.findMany(queryOptions)
}

export function upsertDocument(document: Document): Promise<PrismaDocument> {
  const documentData: any = document.toPrisma().data
  documentData.Data = document.data || Buffer.from('')

  return prisma.document.upsert({
    where: {
      Id: document.toPrisma().id
    },
    update: documentData,
    create: documentData
  })
}

export function updateDocument(document: Document): Promise<PrismaDocument> {
  const { id, data } = document.toPrisma()

  return prisma.document.update({
    where: {
      Id: id
    },
    data: data
  })
}

export function deleteDocument(id: string): Promise<PrismaDocument> {
  return prisma.document.delete({
    where: {
      Id: id
    }
  })
}

/**
 * Updates only the shared property of a document
 * @param id The document ID
 * @param shared The new shared status
 * @returns Promise with the updated document
 */
export function updateDocumentShared(id: string, shared: boolean): Promise<PrismaDocument> {
  return prisma.document.update({
    where: {
      Id: id
    },
    data: {
      shared: shared
    }
  })
}

/**
 * Updates all documents inside the database that have a ByteSize of -1.
 */
export function updateAllByteSizeFields(): Promise<void> {
  return new Promise((resolve, reject) => {
    prisma.document
      .findMany({
        where: {
          ByteSize: -1
        }
      })
      .then(async res => {
        log.debug('Update: ' + String(res.length) + ' items')

        for (const item of res) {
          log.debug(`Updating: ${item.Id}`)

          await prisma.document.update({
            where: {
              Id: item.Id
            },
            data: {
              ByteSize: Buffer.byteLength(item.Data)
            }
          })
        }

        resolve()
      })
      .catch(reject)
  })
}

export async function resizeAllPhotos() {
  const batchSize = 100
  let skip = 0
  let resizedCount = 0
  let hasMorePhotos = true

  while (hasMorePhotos) {
    const photos = await prisma.document.findMany({
      where: { DocumentType: 'photo' },
      skip: skip,
      take: batchSize
    })

    if (photos.length === 0) {
      hasMorePhotos = false
      break
    }

    let batchResizedCount = 0

    await Promise.all(
      photos.map(async photo => {
        if (!photo.Data) return null

        try {
          const metadata = await sharp(photo.Data).metadata()
          if ((metadata.width && metadata.width > 256) || (metadata.height && metadata.height > 256)) {
            const resizedPhoto = await sharp(photo.Data).resize(256, 256).toBuffer()
            await prisma.document.update({
              where: { Id: photo.Id },
              data: {
                Data: resizedPhoto,
                ByteSize: Buffer.byteLength(resizedPhoto)
              }
            })
            batchResizedCount++
            return true
          }
        } catch (err: any) {
          log.error(`Failed to resize photo with id: ${photo.Id}, error: ${err.message}`)
        }
        return null
      })
    )

    resizedCount += batchResizedCount
    skip += batchSize
  }

  return resizedCount
}
