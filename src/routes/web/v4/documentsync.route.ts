import { Router } from 'express'
import httpStatus from 'http-status'
import Jo<PERSON> from 'joi'
import { APIError } from '../../../util'
import DocumentTypes from '../../../model/DocumentType'
import {
  upsertDocument,
  getDocument,
  updateDocument,
  updateDocumentShared,
  resizeAllPhotos
} from '../../../controller/doc.controller'
import Document from '../../../model/mappings/Doc'
import {
  addDocumentReferenceToCandidates,
  deleteDocumentRefernceFromCandidates
} from '../../../controller/candidate.controller'
import {
  addDocumentReferenceToJobApplications,
  deleteDocumentRefernceFromJobApplications
} from '../../../controller/job-application.controller'
import {
  addDocumentReferenceToProjects,
  deleteDocumentRefernceFromProjects
} from '../../../controller/project.controller'

const router: Router = Router()

// JOI Helpers ==========================================
const docIdSchema = Joi.string().min(18).max(18).required()

// regex expression to validate date an time using the format YYYY-MM-DD HH:MM:SS
const sfDateTimeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/

const sfIdSchema = Joi.string().min(18).max(18)
const linkedRecordSchema = Joi.array().items(sfIdSchema.required())

function validateDocumentId(docId: string) {
  const validationResult = docIdSchema.validate(docId)
  if (validationResult.error) throw new APIError(httpStatus.BAD_REQUEST, validationResult.error.message)
}

// ======================================================

// eslint-disable-next-line @typescript-eslint/no-explicit-any
router.post('/:Id', async (req: any, res: any) => {
  const docId = req.params.Id
  validateDocumentId(docId)

  // const overwrite = req.query.overwrite === '' || req.query.overwrite === 'true'

  const schema = Joi.object({
    title: Joi.string().required(),
    sfType: Joi.string()
      .valid(...Object.keys(DocumentTypes))
      .default('sonstiges'),
    sfTimestamp: Joi.string().regex(sfDateTimeRegex).required(),
    fileExtension: Joi.string().required(),
    linkedRecords: Joi.object({
      applications: linkedRecordSchema,
      candidates: linkedRecordSchema,
      projects: linkedRecordSchema,
      emmyUsers: Joi.string().min(1)
    })
      .min(1)
      .required(),
    data: Joi.string().base64().optional()
  })

  const { error, value } = schema.validate(req.body)
  if (error) {
    res.status(httpStatus.BAD_REQUEST).send({ success: false, message: error.message })
    return
  }

  const hasData = req.body.data && req.body.data.length > 0
  let crudType = 'create'
  if (hasData) {
    const doc = Document.createFromRequest(docId, value)
    const possibleDoc = await getDocument(docId)
    if (possibleDoc !== null) {
      await updateDocument(doc)
      crudType = 'update'
    }

    await upsertDocument(doc)
  } else {
    crudType = 'update'
  }

  if (value.linkedRecords.candidates) await addDocumentReferenceToCandidates(value.linkedRecords.candidates, docId)
  if (value.linkedRecords.applications)
    await addDocumentReferenceToJobApplications(value.linkedRecords.applications, docId)

  // this route is invoked only when document is shared on EmmyCore so we are setting shared flag
  await updateDocumentShared(docId, true)

  res.send({ success: true, message: `Document '${docId}' succesfully transmitted`, crudType })
})

// eslint-disable-next-line @typescript-eslint/no-explicit-any
router.delete('/:Id', async (req: any, res: any) => {
  const docId = req.params.Id
  validateDocumentId(docId)

  const possibleDoc = await getDocument(docId)
  if (possibleDoc === null) {
    res.status(httpStatus.NOT_FOUND).send({ success: false, message: `Document with id '${docId}' not found` })
    return
  }

  await updateDocumentShared(docId, false)

  // set refernces
  await deleteDocumentRefernceFromJobApplications(docId)
  await deleteDocumentRefernceFromProjects(docId)

  res.send({ success: true, message: `Deleted document '${docId}'` })
})

router.post('/ref/:Id', async (req: any, res: any) => {
  const docId = req.params.Id
  validateDocumentId(docId)

  const schema = Joi.object({
    candidates: linkedRecordSchema,
    applications: linkedRecordSchema,
    projects: linkedRecordSchema,
    emmyUsers: Joi.string().min(1)
  }).min(1)

  const { error, value } = schema.validate(req.body)
  if (error) {
    res.status(httpStatus.BAD_REQUEST).send({ success: false, message: error.message })
    return
  }

  if (value.candidates) await addDocumentReferenceToCandidates(value.candidates, docId)
  if (value.applications) await addDocumentReferenceToJobApplications(value.applications, docId)
  if (value.projects) await addDocumentReferenceToProjects(value.projects, docId)

  await updateDocumentShared(docId, true)

  res.send({ success: true, message: `Added reference to document '${docId}'` })
})

router.delete('/ref/:Id', async (req: any, res: any) => {
  const docId = req.params.Id
  validateDocumentId(docId)

  const schema = Joi.object({
    candidates: linkedRecordSchema,
    applications: linkedRecordSchema,
    projects: linkedRecordSchema,
    emmyUsers: Joi.string().min(1)
  }).min(1)

  const { error, value } = schema.validate(req.body)
  if (error) {
    res.status(httpStatus.BAD_REQUEST).send({ success: false, message: error.message })
    return
  }

  if (value.candidates) await deleteDocumentRefernceFromCandidates(docId)
  if (value.applications) await deleteDocumentRefernceFromJobApplications(docId)
  if (value.projects) await deleteDocumentRefernceFromProjects(docId)

  await updateDocumentShared(docId, false)

  res.send({ success: true, message: `Deleted references from document '${docId}'` })
})

router.post('/maintenance/optimizePhotos', async (req: any, res: any) => {
  const resizedCount = await resizeAllPhotos()
  res.send({ success: true, message: `Successfully resized ${resizedCount} photos` })
})

export default router
